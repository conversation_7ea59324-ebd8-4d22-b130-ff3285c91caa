import React from 'react';
import { Table, Button, Tooltip, Tag } from 'antd';
import { InfoCircleOutlined, MenuFoldOutlined } from '@ant-design/icons';
import styled from 'styled-components';
// 复用审批工单页面的表格样式包装器，确保风格一致
import { AuditedTable } from '../../../components/OrderTable/styles';
import { getRowClassName } from '../../../util/slaHelper';
import { SORT_FIELDS } from '../../../constants/order_constants';

// 将内部排序标记('asc'/'desc')转换为 Ant Design 需要的('ascend'/'descend')
const toAntSortOrder = (order) => {
  if (order === 'asc') return 'ascend';
  if (order === 'desc') return 'descend';
  return null;
};

// 工单状态映射函数 - 完整的中英文映射
const getOrderStatusDisplay = (status) => {
  const statusMap = {
    // 英文到中文映射
    'approving': '审批中',
    'completed': '已完结', 
    'rejected': '已驳回',
    'failed': '已失败',
    'pending': '待审批',
    'processing': '处理中',
    'cancelled': '已取消',
    'approved': '已审批',
    'submitted': '已提交',
    'draft': '草稿',
    
    // 兼容中文状态（直接返回）
    '审批中': '审批中',
    '已完结': '已完结',
    '已驳回': '已驳回',
    '已失败': '已失败',
    '待审批': '待审批',
    '处理中': '处理中',
    '已取消': '已取消',
    '已审批': '已审批',
    '已提交': '已提交',
    '草稿': '草稿'
  };
  return statusMap[status] || status;
};

// 获取状态对应的Ant Design Tag颜色（与审批工单页面完全一致）
const getStatusTagColor = (status) => {
  const displayStatus = getOrderStatusDisplay(status);
  switch (displayStatus) {
    case '审批中':
      return 'blue'; // 审批中=蓝
    case '待审批':
      return 'orange'; // 待审批=橙
    case '处理中':
      return 'processing'; // 处理中=蓝色动画
    case '已完结':
      return 'success'; // 已完结=绿
    case '已驳回':
      return 'warning'; // 已驳回=黄
    case '已失败':
      return 'error'; // 已失败=红
    case '已取消':
      return 'default'; // 已取消=灰
    case '已审批':
      return 'cyan'; // 已审批=青
    case '已提交':
      return 'geekblue'; // 已提交=极客蓝
    case '草稿':
      return 'purple'; // 草稿=紫
    default:
      return 'default';
  }
};

// 工单状态标签单元格组件（使用Ant Design Tag，与审批工单页面完全一致）
const OrderStatusCell = ({ status, children }) => {
  const displayStatus = getOrderStatusDisplay(status);
  const tagColor = getStatusTagColor(status);
  
  return (
    <div className="order-status-cell">
      <Tag 
        className="role-tag" 
        color={tagColor} 
        style={{ 
          position: 'absolute',
          top: '2px', 
          left: '2px',
          fontSize: '10px',
          lineHeight: '1',
          margin: 0,
          zIndex: 1
        }}
      >
        [{displayStatus}]
      </Tag>
      <div className="order-status-cell-content">
        {children}
      </div>
    </div>
  );
};

// 使用与审批工单页面一致的外层样式容器，并在其基础上补充SLA高亮等特有样式
const TableContainer = styled(AuditedTable)`
  /* 表格内部顶部栏样式 */
  .table-topbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 4px;
  }
  
  .table-topbar-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .table-topbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  // SLA高亮样式 - 提高优先级，覆盖默认斑马纹
  .ant-table-tbody > tr.sla-warning > td {
    background-color: #fff7e6 !important;
  }
  
  .ant-table-tbody > tr.sla-critical > td {
    background-color: #fff2f0 !important;
  }
  
  // 工单状态单元格样式（与审批工单页面一致）
  .order-status-cell {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 24px;
    padding-top: 14px; /* 为左上角标签留出空间，与审批工单页面一致 */
  }
  
  .order-status-cell-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
`;



const CcListCell = styled.div`
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

/**
 * 工单管理页面专属的表格组件
 * 列定义硬编码在组件内部，专为工单管理页面设计
 */
const ManageOrderTable = ({
  loading = false,
  dataSource = [],
  pagination = {},
  sorter = {},
  onTableChange,
  onViewDetail,
  // 新增：表格内部顶部栏内容（左/右），用于贴合表格上方展示筛选条件和SLA说明
  headerLeft = null,
  headerRight = null
}) => {

  // 获取列配置
  const getColumns = () => {
    return [
      {
        title: '单号',
        dataIndex: 'order_id',
        key: 'order_id',
        width: 180,
        render: (text, record) => (
          <OrderStatusCell status={record.status}>
            <span>{text}</span>
          </OrderStatusCell>
        )
      },
      {
        title: '工单标题',
        dataIndex: 'title',
        key: 'title',
        width: 250,
        ellipsis: {
          showTitle: false
        },
        render: (text) => (
          <Tooltip title={text} placement="topLeft">
            {text}
          </Tooltip>
        )
      },
      {
        title: '工单类型',
        dataIndex: 'order_type',
        key: 'order_type',
        width: 120,
        align: 'center'
      },
      {
        title: '总节点数',
        dataIndex: 'total_nodes',
        key: 'total_nodes',
        width: 100,
        align: 'center'
      },
      {
        title: '当前节点',
        dataIndex: 'current_node',
        key: 'current_node',
        width: 150,
        align: 'center',
        render: (text) => text || '-'
      },
      {
        title: '申请人',
        dataIndex: 'applicant',
        key: 'applicant',
        width: 120,
        align: 'center'
      },
      {
        title: '运维负责人',
        dataIndex: 'ops_lead_email',
        key: 'ops_lead_email',
        width: 150,
        align: 'center',
        render: (text) => text || '-'
      },
      {
        title: '抄送人',
        dataIndex: 'cc_list',
        key: 'cc_list',
        width: 200,
        align: 'center',
        render: (ccList) => {
          if (!ccList || ccList.length === 0) {
            return '-';
          }
          
          const displayText = ccList.length > 3 
            ? `${ccList.slice(0, 3).join(', ')}...`
            : ccList.join(', ');
            
          return (
            <Tooltip title={ccList.join(', ')} placement="topLeft">
              <CcListCell>{displayText}</CcListCell>
            </Tooltip>
          );
        }
      },
      {
        title: '申请时间',
        dataIndex: 'apply_time',
        key: 'apply_time',
        width: 180,
        sorter: true,
        sortDirections: ['ascend', 'descend'],
        sortOrder: sorter.field === SORT_FIELDS.APPLY_TIME ? toAntSortOrder(sorter.order) : null,
        align: 'center',
        render: (text) => {
          if (!text) return '-';
          try {
            return new Date(text).toLocaleString('zh-CN');
          } catch (e) {
            return text;
          }
        }
      },
      {
        title: '最新更新时间',
        dataIndex: 'latest_update_time',
        key: 'latest_update_time',
        width: 180,
        sorter: true,
        sortDirections: ['ascend', 'descend'],
        sortOrder: sorter.field === SORT_FIELDS.LATEST_UPDATE_TIME ? toAntSortOrder(sorter.order) : null,
        align: 'center',
        render: (text) => {
          if (!text) return '-';
          try {
            return new Date(text).toLocaleString('zh-CN');
          } catch (e) {
            return text;
          }
        }
      },
      {
        title: '总耗时',
        dataIndex: 'total_duration_display',
        key: 'total_duration',
        width: 120,
        sorter: true,
        sortDirections: ['ascend', 'descend'],
        sortOrder: sorter.field === SORT_FIELDS.TOTAL_DURATION ? toAntSortOrder(sorter.order) : null,
        align: 'center',
        render: (text) => text || '-'
      },
      {
        title: '当前节点停留时长',
        dataIndex: 'current_node_stay_duration_display',
        key: 'current_node_stay_duration',
        width: 180,
        sorter: true,
        sortDirections: ['ascend', 'descend'],
        sortOrder: sorter.field === SORT_FIELDS.CURRENT_NODE_STAY_DURATION ? toAntSortOrder(sorter.order) : null,
        align: 'center',
        render: (text) => text || '-'
      },
      {
        title: '详情',
        key: 'action',
        width: 80,
        fixed: 'right',
        align: 'center',
        render: (_, record) => (
          <Button 
            type="primary" 
            size="small"
            onClick={() => onViewDetail && onViewDetail(record)}
            icon={<MenuFoldOutlined />}
          >
            详情
          </Button>
        )
      }
    ];
  };



  return (
    <TableContainer>
      {(headerLeft || headerRight) && (
        <div className="table-topbar">
          <div className="table-topbar-left">{headerLeft}</div>
          <div className="table-topbar-right">{headerRight}</div>
        </div>
      )}
      <Table
        loading={loading}
        dataSource={dataSource}
        columns={getColumns()}
        pagination={false}  // 关闭内置分页，使用外部分页组件
        onChange={onTableChange}
        showSorterTooltip={false}
        rowKey={(record, index) => `${record.order_id}-${index}`}
        rowClassName={getRowClassName}
        // 与审批工单页面保持一致的滚动宽度
        scroll={{ x: 1200 }}
        size="middle"
        // 强制刷新表格数据，避免缓存问题
        key={`table-${dataSource.length}-${loading ? 'loading' : 'loaded'}`}
      />
    </TableContainer>
  );
};

export default ManageOrderTable;
