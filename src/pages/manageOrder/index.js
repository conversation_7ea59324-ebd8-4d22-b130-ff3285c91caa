import React, { Component } from 'react';
import moment from 'moment';
import { Button, message, Modal, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { isEqual } from 'lodash';
import styled from 'styled-components';

// 导入组件
import SearchForm from '../../components/SearchForm';
import OrderDetailDrawer from '../../components/OrderDetailDrawer';
import ManageOrderTable from './components/ManageOrderTable';
import FilterTagList from './components/FilterTagList';
import OrderPagination from '../../components/OrderPagination';

// 导入服务和工具
import { getManageOrders } from '../../services/order_service';
import { 
  DEFAULT_PAGINATION, 
  DEFAULT_SORTER,
  SORT_FIELDS,
  SORT_ORDER 
} from '../../constants/order_constants';

// 页面布局样式定义
const PageContainer = styled.div`
  /* 与审批工单页面保持一致的布局 */
`;

const SearchFormWrapper = styled.div``;

const TableSection = styled.div`
  position: relative;
`;

const TableHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center; /* 改为居中对齐 */
  margin-bottom: 0; /* 移除间距，让SLA说明紧贴表格 */
  min-height: 32px; /* 减小高度 */
  height: 32px; /* 减小固定高度 */
`;

// 将字符串安全转换为 moment 对象；非法或空值返回 null
const toMomentOrNull = (v) => {
  if (!v || typeof v !== 'string') return null;
  const m = moment(v);
  return m.isValid() ? m : null;
};

const FilterTagSection = styled.div`
  flex: 1;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  min-height: 20px; /* 与表格容器padding-top一致，避免撑高造成视觉间距差异 */
  line-height: 20px;
  
  // 紧凑化标签样式（若内部是Tag或小组件，避免占用过高空间）
  .ant-tag, .ant-badge, .ant-typography {
    line-height: 18px;
    height: 18px;
  }
`;

const SlaInfoSection = styled.div`
  flex-shrink: 0;
  margin-left: 16px;
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
`;

/**
 * 工单管理页面主容器
 * 核心状态管理与逻辑编排中心
 */
class ManageOrderPage extends Component {
  constructor(props) {
    super(props);
    
    this.state = {
      // 权限和加载状态
      hasPermission: true, // 暂时跳过权限检查
      isLoading: false,
      
      // 数据状态
      dataSource: [],
      pagination: { ...DEFAULT_PAGINATION },
      sorter: { ...DEFAULT_SORTER },
      
      // 筛选状态 - 核心的双状态模型
      pendingFilters: {},
      appliedFilters: {},
      
      // 下拉选项数据
      availableOptions: {
        available_order_types: [],
        available_applicants: [],
        available_ops_leads: [],
        available_cc_users: []
      },
      
      // 统计数据
      statistics: {
        total: 0,
        pending: 0,      // 审批中 + 待审批 + 处理中
        completed: 0,    // 已完结  
        rejected: 0,     // 已驳回
        failed: 0,       // 已失败
        cancelled: 0     // 已取消
      },
      
      // 详情抽屉状态
      selectedOrderId: null,
      selectedOrderType: null,
      

    };
  }

  componentDidMount() {
    this.loadInitialData();
  }

  // 加载初始数据
  loadInitialData = async () => {
    const initialParams = {
      page: this.state.pagination.current,
      page_size: this.state.pagination.pageSize,
      sort_by: this.state.sorter.field,
      sort_order: this.state.sorter.order
    };
    // 启用后端联调：首屏加载从后端获取数据
    await this.fetchManageOrders(initialParams);
  };

  // 统一清洗请求参数，避免传入无效值导致接口或渲染异常
  normalizeRequestParams = (rawParams = {}) => {
    try {
      const params = { ...rawParams };
      // 1) 去除空字符串、null、undefined
      Object.keys(params).forEach((key) => {
        const v = params[key];
        if (v === undefined || v === null) {
          delete params[key];
          return;
        }
        if (typeof v === 'string') {
          const trimmed = v.trim();
          if (trimmed === '') {
            delete params[key];
          } else {
            params[key] = trimmed;
          }
        } else if (Array.isArray(v)) {
          if (v.length === 0) {
            delete params[key];
          }
        }
      });
      // 2) 日期允许单端存在：不再强制成对
      return params;
    } catch (e) {
      // 出错时回退原参数，避免中断
      return rawParams || {};
    }
  };

  // 获取工单管理列表数据
  fetchManageOrders = async (params) => {
    // 立即清空数据源和统计信息，防止显示旧数据
    this.setState({ 
      isLoading: true, 
      dataSource: [], 
      statistics: {
        total: 0,
        pending: 0,
        completed: 0,
        rejected: 0,
        failed: 0,
        cancelled: 0
      }
    });
    
    try {
      const safeParams = this.normalizeRequestParams(params);
      // 调试日志：请求入参
      // eslint-disable-next-line no-console
      console.debug('[ManageOrder] fetchManageOrders params:', JSON.stringify(safeParams));
      const result = await getManageOrders(safeParams);
      
      if (result.success) {
        const { data } = result;
        // 计算统计数据
        const statistics = this.calculateStatistics(data.orders || [], data.total || 0);
        
        this.setState({
          dataSource: data.orders || [],
          pagination: {
            ...this.state.pagination,
            total: data.total || 0,
            current: safeParams.page
          },
          availableOptions: {
            available_order_types: data.available_order_types || [],
            available_applicants: data.available_applicants || [],
            available_ops_leads: data.available_ops_leads || [],
            available_cc_users: data.available_cc_users || []
          },
          statistics: statistics
        });
        // 调试日志：响应摘要
        // eslint-disable-next-line no-console
        console.debug('[ManageOrder] fetchManageOrders success: total=', data.total, 'orders=', (data.orders||[]).length);
        // eslint-disable-next-line no-console
        console.debug('[ManageOrder] setState dataSource:', data.orders);
        // eslint-disable-next-line no-console
        console.debug('[ManageOrder] currentState after setState:', this.state.dataSource);
      } else {
        message.error(result.message || '获取工单列表失败');
        this.setState({
          dataSource: [],
          pagination: { ...this.state.pagination, total: 0 }
        });
      }
    } catch (error) {
      console.error('获取工单列表异常:', error);
      message.error('网络请求失败，请稍后重试');
      this.setState({
        dataSource: [],
        pagination: { ...this.state.pagination, total: 0 }
      });
    } finally {
      this.setState({ isLoading: false });
    }
  };

  // 计算统计数据
  calculateStatistics = (orders, total) => {
    const statistics = {
      total: total,
      pending: 0,      // 审批中 + 待审批 + 处理中
      completed: 0,    // 已完结
      rejected: 0,     // 已驳回
      failed: 0,       // 已失败
      cancelled: 0     // 已取消
    };

    // 根据每条工单的status字段计算统计
    orders.forEach(order => {
      const status = order.status;
      if (status === 'approving') {
        statistics.pending++;
      } else if (status === 'completed') {
        statistics.completed++;
      } else if (status === 'rejected') {
        statistics.rejected++;
      } else if (status === 'failed') {
        statistics.failed++;
      }
    });

    return statistics;
  };

  // 处理搜索表单变化
  handleSearchFormChange = (formData) => {
    // 合并并清洗：允许单端日期
    const merged = { ...this.state.pendingFilters, ...formData };
    const cleaned = { ...merged };
    Object.keys(cleaned).forEach((k) => {
      const v = cleaned[k];
      // 仅清理空值与空数组，保留字符串的原始输入（包括空格）避免回写覆盖打断输入
      if (v === null || v === undefined) { delete cleaned[k]; return; }
      if (Array.isArray(v) && v.length === 0) { delete cleaned[k]; return; }
    });
    this.setState({ pendingFilters: cleaned });
  };

  // 处理搜索按钮点击
  handleSearch = (additionalParams = {}) => {
    const { pendingFilters, pagination, sorter } = this.state;
    
    // 合并搜索参数（现在SearchForm直接使用后端字段名）
    const searchParams = {
      ...pendingFilters,
      ...additionalParams,
      page: 1, // 搜索时重置到第一页
      page_size: pagination.pageSize,
      sort_by: sorter.field,
      sort_order: sorter.order
    };
    
    // 执行搜索
    this.fetchManageOrders(searchParams);
    
    // 更新状态：合并的参数变为 appliedFilters
    const mergedFilters = { ...pendingFilters, ...additionalParams };
    this.setState({
      appliedFilters: { ...mergedFilters },
      pendingFilters: { ...mergedFilters },
      pagination: { ...pagination, current: 1 }
    });
  };

  // 处理重置按钮点击
  handleReset = () => {
    const resetParams = {
      page: 1,
      page_size: this.state.pagination.pageSize,
      sort_by: DEFAULT_SORTER.field,
      sort_order: DEFAULT_SORTER.order
    };
    
    // 重置所有状态
    this.setState({
      pendingFilters: {},
      appliedFilters: {},
      sorter: { ...DEFAULT_SORTER },
      pagination: { ...this.state.pagination, current: 1 }
    });
    
    // 执行重置查询
    this.fetchManageOrders(resetParams);
  };

  // 处理表格变化（分页、排序）
  handleTableChange = (pagination, filters, sorter) => {
    const { pendingFilters, appliedFilters } = this.state;
    
    // 若存在未提交筛选条件，弹框确认是否应用后再排序
    const hasUnsubmittedFilters = !isEqual(pendingFilters, appliedFilters);
    if (hasUnsubmittedFilters && (sorter && (sorter.columnKey || sorter.field))) {
      Modal.confirm({
        title: '确认操作',
        content: '您有未提交的筛选条件。要立即应用这些条件并排序吗？',
        onOk: () => {
          // 应用未提交筛选并执行本次排序
          const fieldFromAnt = sorter.columnKey || sorter.field;
          const orderFromAnt = sorter.order; // 可能为 undefined（表示清空）
          const prevSorter = this.state.sorter || {};
          const nextSorter = { ...prevSorter };
          if (!orderFromAnt) {
            // 仅两态：当 AntD 送来 undefined（清空）时，我们改为在两态间切换
            if (prevSorter.field === fieldFromAnt) {
              nextSorter.field = fieldFromAnt;
              nextSorter.order = prevSorter.order === SORT_ORDER.ASC ? SORT_ORDER.DESC : SORT_ORDER.ASC;
            } else {
              nextSorter.field = fieldFromAnt;
              nextSorter.order = SORT_ORDER.ASC; // 新列默认升序
            }
          } else {
            nextSorter.field = fieldFromAnt;
            nextSorter.order = orderFromAnt === 'ascend' ? SORT_ORDER.ASC : SORT_ORDER.DESC;
          }
          this.setState({ sorter: nextSorter, appliedFilters: { ...pendingFilters } }, () => {
            this.fetchManageOrders({
              ...this.state.appliedFilters,
              page: this.state.pagination.current,
              page_size: this.state.pagination.pageSize,
              sort_by: nextSorter.field,
              sort_order: nextSorter.order
            });
          });
        },
        onCancel: () => {
          // 取消：保持现状，不执行
        }
      });
      return;
    }
    
    // 没有未提交的筛选条件，直接处理表格变化
    const newPagination = { ...this.state.pagination };
    const newSorter = { ...this.state.sorter };
    
    if (pagination) {
      newPagination.current = pagination.current;
      newPagination.pageSize = pagination.pageSize;
    }
    
    if (sorter && (sorter.columnKey || sorter.field)) {
      const fieldFromAnt = sorter.columnKey || sorter.field; // 优先使用与列 key 对齐的 columnKey
      if (!sorter.order) {
        // 仅两态：当 AntD 送来 undefined（清空）时，我们改为在两态间切换
        if (newSorter.field === fieldFromAnt && newSorter.order) {
          newSorter.field = fieldFromAnt;
          newSorter.order = newSorter.order === SORT_ORDER.ASC ? SORT_ORDER.DESC : SORT_ORDER.ASC;
        } else {
          newSorter.field = fieldFromAnt;
          newSorter.order = SORT_ORDER.ASC; // 新列默认升序
        }
      } else {
        newSorter.field = fieldFromAnt;
        newSorter.order = sorter.order === 'ascend' ? SORT_ORDER.ASC : SORT_ORDER.DESC;
      }
    }
    
    this.setState({
      pagination: newPagination,
      sorter: newSorter
    });
    
    // 使用"已生效的筛选条件"和新的分页/排序参数（不自动应用未提交的pendingFilters）
    const requestParams = {
      ...appliedFilters,
      page: newPagination.current,
      page_size: newPagination.pageSize,
      sort_by: newSorter.field,
      sort_order: newSorter.order
    };
    
    this.fetchManageOrders(requestParams);
  };

  // 处理筛选标签移除
  handleFilterTagRemove = (fieldKey) => {
    const { appliedFilters, pendingFilters } = this.state;
    
    // 判断是移除已生效的条件还是准备中的条件
    const isAppliedTag = isEqual(appliedFilters[fieldKey], pendingFilters[fieldKey]);
    
    if (isAppliedTag) {
      // 移除已生效的条件：立即重新查询
      const newAppliedFilters = { ...appliedFilters };
      const newPendingFilters = { ...pendingFilters };
      // 仅删除对应字段，允许单端日期存在或被单独删除
      delete newAppliedFilters[fieldKey];
      delete newPendingFilters[fieldKey];
      
      this.setState({
        appliedFilters: newAppliedFilters,
        pendingFilters: newPendingFilters
      }, () => {
        // 状态更新完成后再执行查询，确保状态同步
        const requestParams = {
          ...newAppliedFilters,
          page: 1,
          page_size: this.state.pagination.pageSize,
          sort_by: this.state.sorter.field,
          sort_order: this.state.sorter.order
        };
        
        this.fetchManageOrders(requestParams);
      });
    } else {
      // 移除准备中的条件：仅更新前端状态
      const newPendingFilters = { ...pendingFilters };
      delete newPendingFilters[fieldKey];
      
      // 使用setState回调确保状态更新完成，解决异步状态更新导致的同步问题
      this.setState({ pendingFilters: newPendingFilters }, () => {
        // 状态更新完成，确保后续操作能获取到最新状态
      });
    }
  };

  // 处理多选标签子项移除
  handleMultiSelectItemRemove = (fieldKey, itemValue) => {
    const { appliedFilters, pendingFilters } = this.state;
    const currentValues = pendingFilters[fieldKey] || [];
    const newValues = currentValues.filter(value => value !== itemValue);
    
    // 判断是移除已生效的条件还是准备中的条件
    const isAppliedTag = isEqual(appliedFilters[fieldKey], pendingFilters[fieldKey]);
    
    if (isAppliedTag) {
      // 移除已生效的条件：立即重新查询
      const newAppliedFilters = {
        ...appliedFilters,
        [fieldKey]: newValues
      };
      const newPendingFilters = {
        ...pendingFilters,
        [fieldKey]: newValues
      };
      
      // 如果数组为空，则删除该字段
      if (newValues.length === 0) {
        delete newAppliedFilters[fieldKey];
        delete newPendingFilters[fieldKey];
      }
      
      this.setState({
        appliedFilters: newAppliedFilters,
        pendingFilters: newPendingFilters
      });
      
      const requestParams = {
        ...newAppliedFilters,
        page: 1,
        page_size: this.state.pagination.pageSize,
        sort_by: this.state.sorter.field,
        sort_order: this.state.sorter.order
      };
      
      this.fetchManageOrders(requestParams);
    } else {
      // 移除准备中的条件：仅更新前端状态
      const newPendingFilters = {
        ...pendingFilters,
        [fieldKey]: newValues
      };
      
      // 如果数组为空，则删除该字段
      if (newValues.length === 0) {
        delete newPendingFilters[fieldKey];
      }
      
      this.setState({ pendingFilters: newPendingFilters });
    }
  };



  // 处理查看详情
  handleViewDetail = (record) => {
    this.setState({
      selectedOrderId: record.order_id,
      selectedOrderType: record.order_type
    });
  };

  // 关闭详情抽屉
  handleDetailDrawerClose = () => {
    this.setState({
      selectedOrderId: null,
      selectedOrderType: null
    });
  };

  /**
   * 渲染SLA信息说明
   */
  renderSlaInfo = () => {
    const getSlaTooltipText = () => {
      return (
        <div>
          <div>SLA高亮说明（仅对审批中工单生效）：</div>
          <div>🟡 警告（淡黄色）：当前节点停留时长 &gt; 1天 或 总耗时 &gt; 3天</div>
          <div>🔴 严重（淡红色）：当前节点停留时长 &gt; 2天 或 总耗时 &gt; 5天</div>
        </div>
      );
    };

    return (
      <Tooltip 
        title={getSlaTooltipText()} 
        placement="topLeft" 
        trigger={["hover"]}
        mouseEnterDelay={0.1}
      >
        <InfoCircleOutlined style={{ color: '#1890ff', cursor: 'pointer', fontSize: '16px' }} />
      </Tooltip>
    );
  };

  render() {
    const {
      isLoading,
      dataSource,
      pagination,
      sorter,
      pendingFilters,
      appliedFilters,
      availableOptions,
      selectedOrderId,
      selectedOrderType,
      statistics
    } = this.state;

    return (
      <PageContainer>
        {/* 搜索表单 - 与审批工单页面保持一致的结构 */}
        <SearchFormWrapper>
          <SearchForm
            initialSearchParams={{
              // 仅同步日期，文本输入交由子组件自身受控，避免空格被回写覆盖
              orderId: pendingFilters.order_id || '',
              title: pendingFilters.title || '',
              appliedStartDate: toMomentOrNull(pendingFilters.applied_start_date),
              appliedEndDate: toMomentOrNull(pendingFilters.applied_end_date)
            }}
            onFormChange={this.handleSearchFormChange}
            showAdvancedFields={true}
            onSearch={this.handleSearch}
            onReset={this.handleReset}
            loading={isLoading}
            // 工单管理页面的高级筛选字段配置
            orderTypeFilters={(availableOptions.available_order_types || []).map(item => {
              if (typeof item === 'string') {
                return { text: item, value: item };
              } else if (item && typeof item === 'object') {
                return { text: item.order_type_name || item.order_type, value: item.order_type };
              }
              return item;
            })}
            statusOptions={[
              { label: '审批中', value: 'approving' },
              { label: '已完结', value: 'completed' },
              { label: '已驳回', value: 'rejected' },
              { label: '已失败', value: 'failed' }
            ]}
            // 传递当前的筛选状态
            pendingFilters={pendingFilters}
            appliedFilters={appliedFilters}
            onFilterTagRemove={this.handleFilterTagRemove}
            // 传递工单管理页面的选项数据
            availableOptions={availableOptions}
          />
        </SearchFormWrapper>

        {/* 表格区域 - 与审批工单页面保持一致的间距 */}
        <TableSection>
          {/* 工单列表表格 */}
          <ManageOrderTable
            loading={isLoading}
            dataSource={dataSource}
            sorter={sorter}
            onTableChange={this.handleTableChange}
            onViewDetail={this.handleViewDetail}
            // 统计信息和SLA说明移到表格左上角
            headerLeft={
              (
                <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                  {/* 统计数据显示 */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                    <span style={{ fontSize: 16, fontWeight: 600, color: '#111827' }}>工单列表</span>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                      <span style={{ fontSize: 12, color: '#6B7280' }}>总工单数:</span>
                      <span style={{ fontSize: 12, fontWeight: 700, color: '#111827', background: '#F3F4F6', padding: '2px 8px', borderRadius: 9999 }}>{statistics.total}</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                      <span style={{ fontSize: 12, color: '#6B7280' }}>审批中:</span>
                      <span style={{ fontSize: 12, fontWeight: 700, color: '#1D4ED8', background: '#DBEAFE', padding: '2px 8px', borderRadius: 9999 }}>{statistics.pending}</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                      <span style={{ fontSize: 12, color: '#6B7280' }}>已完结:</span>
                      <span style={{ fontSize: 12, fontWeight: 700, color: '#065F46', background: '#D1FAE5', padding: '2px 8px', borderRadius: 9999 }}>{statistics.completed}</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                      <span style={{ fontSize: 12, color: '#6B7280' }}>已驳回:</span>
                      <span style={{ fontSize: 12, fontWeight: 700, color: '#92400E', background: '#FEF3C7', padding: '2px 8px', borderRadius: 9999 }}>{statistics.rejected}</span>
                    </div>
                    {statistics.failed > 0 && (
                      <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                        <span style={{ fontSize: 12, color: '#6B7280' }}>已失败:</span>
                        <span style={{ fontSize: 12, fontWeight: 700, color: '#DC2626', background: '#FEE2E2', padding: '2px 8px', borderRadius: 9999 }}>{statistics.failed}</span>
                      </div>
                    )}
                    {statistics.cancelled > 0 && (
                      <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                        <span style={{ fontSize: 12, color: '#6B7280' }}>已取消:</span>
                        <span style={{ fontSize: 12, fontWeight: 700, color: '#6B7280', background: '#F9FAFB', padding: '2px 8px', borderRadius: 9999 }}>{statistics.cancelled}</span>
                      </div>
                    )}
                  </div>
                  {/* SLA说明移到左上方 */}
                  <SlaInfoSection>
                    {this.renderSlaInfo()}
                  </SlaInfoSection>
                </div>
              )
            }
          />
          
          {/* 分页组件 - 与审批工单页面保持一致 */}
          <OrderPagination
            total={pagination.total}
            current={pagination.current}
            pageSize={pagination.pageSize}
            pageSizeOptions={['10', '20', '30', '50', '100']}
            onPageChange={(page) => this.handleTableChange({ current: page, pageSize: pagination.pageSize }, null, sorter)}
            onPageSizeChange={(current, size) => this.handleTableChange({ current: 1, pageSize: size }, null, sorter)}
            showTotal={false}
          />
        </TableSection>



        {/* 工单详情抽屉 */}
        {selectedOrderId && (
          <OrderDetailDrawer
            key={`${selectedOrderId}-${selectedOrderType}`}
            orderID={selectedOrderId}
            orderType={selectedOrderType}
            visible={true}
            onClose={this.handleDetailDrawerClose}
          />
        )}
      </PageContainer>
    );
  }
}

export default ManageOrderPage;
