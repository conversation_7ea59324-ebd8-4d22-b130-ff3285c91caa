import { Component } from "react";
import { Table } from "antd";
import { AuditedTable } from "@/components/OrderTable/styles";
import OrderDetailDrawer from "@/components/OrderDetailDrawer";
import { requestMyDoneOrder } from "@/request/api";
import RoleTagCell from "@/components/RoleTag/RoleTagCell";
import OrderPagination from "../../components/OrderPagination";
import styled from "styled-components";

// css-js start ↓↓↓
const DoneTable = styled(Table)`
  margin-top: 16px;
  background: #fff;
  border-radius: 12px;
  padding: 0 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  .ant-table-thead > tr > th {
    background-color: #fff;
    font-weight: 500;
    border-bottom: 1px solid #f0f2f5;
  }
  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #f5f7fb;
  }
  .ant-table-tbody > tr:hover > td {
    background-color: #f5f7fb;
  }
`
// css-js end   ↑↑↑

export default class DoneOrder extends Component {
  state = {
    dataSource: [],
    pageSize: 12,
    page: 1,
    total: 0,
    filter_by_role: [], // 为迭代三预留角色筛选参数
  };

  // 获取表格列配置
  getColumns = () => {
    return [
      {
        title: "单号",
        dataIndex: "orderID",
        key: "orderID",
        fixed: "left",
        align: "center",
        render: (text, record) => (
          <RoleTagCell roleType={record.role_type}>
            <span>{text}</span>
          </RoleTagCell>
        ),
        // 更新筛选框标签为申请和抄送
        filters: [
          { text: '[申请]', value: 'APPLICANT' },
          { text: '[抄送]', value: 'CC_TO_ME' },
        ],
        filteredValue: this.state.filter_by_role.length > 0 ? this.state.filter_by_role : null,
        filterMultiple: true, // 支持多选
      },
      {
        title: "工单类型",
        dataIndex: "orderTypeName",
        key: "orderTypeName",
        align: "center",
      },
      {
        title: "总节点数",
        dataIndex: "totalStageNum",
        key: "totalStageNum",
        align: "center",
      },
      {
        title: "当前节点",
        dataIndex: "currentStage",
        key: "currentStage",
        align: "center",
      },
      {
        title: "工单状态",
        dataIndex: "resultDesc",
        key: "resultDesc",
        align: "center",
      },
      {
        title: "申请人",
        dataIndex: "propose",
        key: "propose",
        align: "center",
      },
      {
        title: "运维负责人",
        dataIndex: "opsOwner",
        key: "opsOwner",
        align: "center",
      },
      {
        title: "申请日期",
        dataIndex: "ctime",
        key: "ctime",
        align: "center",
        sorter: (a, b) => a.age - b.age,
      },
      {
        title: "详情",
        key: "detail",
        dataIndex: "detail",
        fixed: "right",
        align: "center",
        render: (text, record, index) => {
          return <OrderDetailDrawer title={"详情"} orderID={record.orderID} orderType={record.orderType} />;
        },
      },
    ];
  };

  // 根据is_cc字段判断角色类型
  getRoleType = (is_cc) => {
    return is_cc === 1 ? 'CC_TO_ME' : 'APPLICANT';
  };

  componentDidMount() {
    this.requestMyDonePageOrder()
  }

  requestMyDonePageOrder = () => {
    requestMyDoneOrder({
      page: this.state.page,
      page_size: this.state.pageSize,
      filter_by_role: this.state.filter_by_role // 传递角色筛选参数
    }).then((data) => {
      var orders = data.orders.map((item, index) => {
        return {
          key: index,
          orderID: item.order_id,
          orderType:item.order_type,
          orderTypeName: item.order_type_name,
          totalStageNum: item.total_stage_num,
          currentStage: item.current_stage,
          resultDesc: item.result_desc,
          propose: item.proposer_email,
          opsOwner: item.ops_owner_email,
          ctime: item.apply_datetime,
          trace: "action",
          role_type: this.getRoleType(item.is_cc), // 根据is_cc字段判断角色类型
        };
      });
      this.setState({ dataSource: orders, total: data.total });
    });
  }

  // 处理分页变化
  handlePageChange = (page) => {
    this.setState({ page: page }, this.requestMyDonePageOrder)
  }

  // 处理每页显示数量变化
  handlePageSizeChange = (current, size) => {
    this.setState({ page: 1, pageSize: size }, this.requestMyDonePageOrder)
  }

  // 处理表格变化（分页、排序、筛选）
  handleTableChange = (pagination, filters, sorter) => {
    // 处理角色筛选逻辑
    const roleFilters = filters.orderID || [];
    this.setState({
      filter_by_role: roleFilters,
      page: 1 // 筛选时重置到第一页
    }, () => {
      this.requestMyDonePageOrder();
    });
  };

  render() {
    return (
      <div>
        <DoneTable
          className="done-order-table"
          dataSource={this.state.dataSource}
          columns={this.getColumns()}
          size={"middle"}
          pagination={false}
          onChange={this.handleTableChange} // 添加表格变化处理
        />
        <OrderPagination
          total={this.state.total}
          current={this.state.page}
          pageSize={this.state.pageSize}
          pageSizeOptions={['12', '20', '30', '50', '100']}
          onPageChange={this.handlePageChange}
          onPageSizeChange={this.handlePageSizeChange}
        />
      </div>

    );
  }
}
