"use client"
import React, { useState, useEffect } from "react";
import { message } from "antd";
import { requestFlowApproval, requestFlowAuditTurn, requestOrderDetail } from "@/request/api";
import styled from "styled-components";
import Cookies from "js-cookie";
import {
  X,
  User,
  FileText,
  MessageSquare,
  CheckCircle,
  Clock,
  AlertCircle,
  UserCheck,
  ThumbsUp,
  ThumbsDown,
  MenuIcon,
} from "lucide-react";

// 自定义UI组件样式 - 模仿shadcn/ui
const CustomButton = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  border: 1px solid transparent;
  padding: 8px 16px;
  height: 36px;
  
  &.variant-default {
    background-color: #0f172a;
    color: #f8fafc;
    &:hover {
      background-color: #1e293b;
    }
  }
  
  &.variant-outline {
    border: 1px solid #e2e8f0;
    background-color: transparent;
    &:hover {
      background-color: #f1f5f9;
    }
  }
  
  &.variant-ghost {
    background-color: transparent;
    &:hover {
      background-color: #f1f5f9;
    }
  }
  
  &.variant-destructive {
    background-color: #dc2626;
    color: #f8fafc;
    &:hover {
      background-color: #b91c1c;
    }
  }
  
  &.size-sm {
    height: 32px;
    padding: 6px 12px;
    font-size: 13px;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const Card = styled.div`
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
`;

const CardHeader = styled.div`
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-bottom: 0;
`;

const CardTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  line-height: 1;
  margin: 0;
`;

const CardContent = styled.div`
  padding: 24px;
  padding-top: 0;
`;

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid transparent;
  
  &.bg-green-100 {
    background-color: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
  }
  
  &.bg-blue-100 {
    background-color: #dbeafe;
    color: #1e40af;
    border-color: #bfdbfe;
  }
  
  &.bg-orange-100 {
    background-color: #fed7aa;
    color: #c2410c;
    border-color: #fdba74;
  }
  
  &.bg-red-100 {
    background-color: #fee2e2;
    color: #dc2626;
    border-color: #fecaca;
  }
  
  &.bg-yellow-100 {
    background-color: #fef3c7;
    color: #d97706;
    border-color: #fde68a;
  }
  
  &.bg-gray-100 {
    background-color: #f3f4f6;
    color: #374151;
    border-color: #e5e7eb;
  }
`;

const CustomTextarea = styled.textarea`
  display: flex;
  min-height: 80px;
  width: 100%;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  padding: 12px;
  font-size: 14px;
  resize: vertical;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
`;

const CustomInput = styled.input`
  display: flex;
  height: 36px;
  width: 100%;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  padding: 8px 12px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
`;

const CustomLabel = styled.label`
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  color: #374151;
`;

// Dialog组件样式
const DialogOverlay = styled.div`
  position: fixed;
  inset: 0;
  z-index: 50;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
`;

const DialogContent = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 32rem;
  max-height: 85vh;
  overflow-y: auto;
`;

const DialogHeader = styled.div`
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-bottom: 16px;
`;

const DialogTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  line-height: 1;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const DialogFooter = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 24px;
  padding-top: 16px;
  
  @media (min-width: 640px) {
    flex-direction: row;
    justify-content: flex-end;
  }
`;

export default function OrderDetailDrawer({ orderID, orderType, visible, onClose, title }) {
  // 状态管理 - 保持原有的所有状态变量
  const [auditButtonVisible, setAuditButtonVisible] = useState(false);
  const [turnAuditButtonVisible, setTurnAuditButtonVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);

  const [remark, setRemark] = useState("");
  const [stageInfos, setStageInfos] = useState([]);
  const [orderInfo, setOrderInfo] = useState({ info: {} });

  const [comment, setComment] = useState("");
  const [showOwnerChangeModal, setShowOwnerChangeModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [newOwnerEmail, setNewOwnerEmail] = useState("");
  const [approvalAction, setApprovalAction] = useState("approve");


  
  const cnMap = {
    "common": {
      "title": [3, "标题"],
      "ops_audit_email": [3, "运维审批人"],
      "apply_msg": [6, "申请理由"],
    },
    "sql_audit_execute": {
      "db_host": [3, "数据库IP"],
      "check_md5": [3, "校验ID"],
      "sql": [6, "sql语句"]
    },
    "sql_audit_file_execute": {
      "db_host": [3, "数据库IP"],
      "check_md5": [3, "校验ID"],
      "sql": [6, "sql语句"]
    },
    "server_jump_impower": {
      "server_ip": [6, "申请登陆权限IP"],
      "apply_msg": [6, "申请理由"],
    },
    "pointed_approver": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "cdn_create_execute": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "domain_resolve_execute": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "long_term_token_new_refresh": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    },
    "long_term_token_view": {
      "approver_email": [6, "审批人"],
      "apply_msg": [6, "申请理由"]
    }
  };

  // 工具函数 - 获取中文映射
  const getCnMap = (orderType) => {
    if (orderType in cnMap) {
      return cnMap[orderType];
    }
    return {
      "apply_msg": [6, "申请理由"]
    };
  };

  // 数据获取函数
  const fetchOrderDetail = async (orderID) => {
    try {
      const userEmail = Cookies.get("user_email");
      const data = await requestOrderDetail({ order_id: orderID });

      if (data === null) {
        return;
      }

      const stageInfosData = data.stage_infos;
      const orderInfoData = data.order_info;

      // 初始化按钮显示状态
      let auditButtonVisibleState = false;
      let turnAuditButtonVisibleState = false;

      if (stageInfosData[orderInfoData.current_stage - 1].stage_operator === userEmail && orderInfoData.result === 0) {
        auditButtonVisibleState = true;
      }
      if (stageInfosData[orderInfoData.current_stage - 1].stage_operator === userEmail && orderInfoData.result === 0 && orderInfoData.current_stage < orderInfoData.total_stage_num) {
        turnAuditButtonVisibleState = true;
      }

      // 解析详情字段
      orderInfoData.info = JSON.parse(orderInfoData.info);
      orderInfoData.info["apply_msg"] = orderInfoData.apply_msg;

      setAuditButtonVisible(auditButtonVisibleState);
      setTurnAuditButtonVisible(turnAuditButtonVisibleState);
      setStageInfos(stageInfosData);
      setOrderInfo(orderInfoData);
      setDrawerVisible(true);
    } catch (err) {
      console.log(err);
    }
  };

  // useEffect替代componentDidMount和componentDidUpdate
  useEffect(() => {
    const isVisible = visible === true;
    if (isVisible && orderID) {
      fetchOrderDetail(orderID);
    }
  }, [visible, orderID]);

  // 状态图标和颜色函数
  const getStatusIcon = (status) => {
    switch (status) {
      case "已完成":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "处理中":
        return <Clock className="h-4 w-4 text-blue-600" />;
      case "待审批":
        return <AlertCircle className="h-4 w-4 text-orange-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "已完成":
        return "bg-green-100 text-green-800 border-green-200";
      case "处理中":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "待审批":
        return "bg-orange-100 text-orange-800 border-orange-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };



  // 事件处理函数 - 保持原有的API调用逻辑
  const handleOwnerChange = async () => {
    try {
      await requestFlowAuditTurn({
        order_id: orderID,
        audit_email: newOwnerEmail.trim()
      });
      message.success("审批人变更成功");
      setShowOwnerChangeModal(false);
      setNewOwnerEmail("");
      // 重新获取数据
      fetchOrderDetail(orderID);
    } catch (err) {
      message.error("审批人变更失败");
      console.log(err);
    }
  };

  const handleApproval = async () => {
    try {
      const result = approvalAction === "approve" ? 1 : 2;
      await requestFlowApproval({
        order_id: orderID,
        result: result,
        remark: remark.trim()
      });
      message.success(`审批${approvalAction === "approve" ? "通过" : "驳回"}成功`);
      setShowApprovalModal(false);
      setApprovalAction("approve");
      setRemark("");
      // 重新获取数据
      fetchOrderDetail(orderID);
    } catch (err) {
      message.error(`审批${approvalAction === "approve" ? "通过" : "驳回"}失败`);
      console.log(err);
    }
  };

  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      setDrawerVisible(false);
    }
  };

  // 如果不可见，不渲染
  const isOpen = visible !== undefined ? visible : drawerVisible;

  return (
    <>
      {/* 只有在没有外部控制时才显示按钮 */}
      {visible === undefined && (
        <CustomButton
          className="variant-default size-sm"
          onClick={() => {
            if (orderID) {
              fetchOrderDetail(orderID);
            }
          }}
        >
          <MenuIcon className="h-4 w-4 mr-2" />
          {title || "详情"}
        </CustomButton>
      )}

      {/* 抽屉内容 - 只有在打开且有数据时才渲染 */}
      {isOpen && orderInfo.order_id && (
        <div className="fixed inset-0 z-50 bg-black/50 flex justify-end">
        <div className="bg-white w-full max-w-2xl h-full overflow-y-auto shadow-xl">
          <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">审批流程跟踪</h2>
            <CustomButton className="variant-ghost size-sm" onClick={handleClose}>
              <X className="h-4 w-4" />
            </CustomButton>
          </div>

          <div className="p-6 space-y-6">
            {/* 操作按钮 */}
            <div className="flex gap-3">
              <CustomButton
                className="variant-outline size-sm"
                onClick={() => setShowOwnerChangeModal(true)}
                style={{ display: turnAuditButtonVisible ? 'flex' : 'none' }}
              >
                <UserCheck className="h-4 w-4 mr-2" />
                负责人变更
              </CustomButton>
              <CustomButton
                className="variant-outline size-sm"
                onClick={() => setShowApprovalModal(true)}
                style={{ display: auditButtonVisible ? 'flex' : 'none' }}
              >
                <ThumbsUp className="h-4 w-4 mr-2" />
                审批
              </CustomButton>
            </div>

            {/* 流程跟踪 */}
            <Card>
              <CardHeader>
                <CardTitle>流程状态</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                      <User className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">提交</span>
                        <span className="text-sm text-gray-500">{orderInfo.applicant || "申请人"}</span>
                      </div>
                    </div>
                  </div>

                  {stageInfos.map((stage, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                        stage.stage_result === 1 ? 'bg-blue-100' : 'bg-gray-100'
                      }`}>
                        <FileText className={`h-4 w-4 ${
                          stage.stage_result === 1 ? 'text-blue-600' : 'text-gray-400'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <span className={`font-medium ${
                            stage.stage_result === 1 ? '' : 'text-gray-400'
                          }`}>{stage.stage_name}</span>
                          <span className={`text-sm ${
                            stage.stage_result === 1 ? 'text-gray-500' : 'text-gray-400'
                          }`}>{stage.stage_operator}</span>
                        </div>
                      </div>
                    </div>
                  ))}

                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                      <CheckCircle className="h-4 w-4 text-gray-400" />
                    </div>
                    <div className="flex-1">
                      <span className="font-medium text-gray-400">Done</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 工单信息 */}
            <Card>
              <CardHeader>
                <CardTitle>工单信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <CustomLabel className="text-sm font-medium text-gray-500">工单ID</CustomLabel>
                    <p className="mt-1 text-sm text-gray-900">{orderInfo.id}</p>
                  </div>
                  <div>
                    <CustomLabel className="text-sm font-medium text-gray-500">工单名称</CustomLabel>
                    <p className="mt-1 text-sm text-gray-900">{orderInfo.title}</p>
                  </div>
                  <div>
                    <CustomLabel className="text-sm font-medium text-gray-500">工单负责人</CustomLabel>
                    <p className="mt-1 text-sm text-gray-900">{orderInfo.assignee}</p>
                  </div>
                  <div>
                    <CustomLabel className="text-sm font-medium text-gray-500">状态</CustomLabel>
                    <div className="mt-1">
                      <Badge className={`${getStatusColor(orderInfo.status)} border`}>
                        {getStatusIcon(orderInfo.status)}
                        <span className="ml-1">{orderInfo.status}</span>
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 工单详情 */}
            <Card>
              <CardHeader>
                <CardTitle>工单详情</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <CustomLabel className="text-sm font-medium text-gray-500">申请理由</CustomLabel>
                    <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-700">
                        {Object.keys(getCnMap(orderType)).map((label, index) => {
                          const cnMapData = getCnMap(orderType);
                          const cnName = cnMapData[label][1];
                          const content = orderInfo.info[label];
                          return (
                            <div key={index} style={{ marginBottom: '8px' }}>
                              <strong>{cnName}:</strong> {content}
                            </div>
                          );
                        })}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 评论 */}
            <Card>
              <CardHeader>
                <CardTitle>评论</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <CustomTextarea
                    placeholder="添加评论..."
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    style={{ minHeight: '100px' }}
                  />
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Ctrl + Enter 快速提交</span>
                    <CustomButton className="size-sm">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      发表评论
                    </CustomButton>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      )}

      {/* 负责人变更弹窗 */}
      {showOwnerChangeModal && (
        <DialogOverlay onClick={() => setShowOwnerChangeModal(false)}>
          <DialogContent onClick={(e) => e.stopPropagation()}>
            <DialogHeader>
              <DialogTitle>
                <UserCheck className="h-5 w-5 text-blue-600" />
                负责人变更
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-6 px-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <User className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-blue-900 mb-1">当前负责人</h4>
                    <p className="text-sm text-blue-700">{orderInfo?.assignee || "未指定"}</p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <CustomLabel className="text-sm font-medium text-gray-700">
                  新负责人邮箱 <span className="text-red-500">*</span>
                </CustomLabel>
                <CustomInput
                  type="email"
                  placeholder="请输入新负责人的邮箱地址"
                  value={newOwnerEmail}
                  onChange={(e) => setNewOwnerEmail(e.target.value)}
                />
                <p className="text-xs text-gray-500">系统将自动发送通知邮件给新负责人</p>
              </div>
            </div>
            <DialogFooter>
              <CustomButton
                className="variant-outline flex-1"
                onClick={() => setShowOwnerChangeModal(false)}
              >
                取消
              </CustomButton>
              <CustomButton
                className="flex-1"
                onClick={handleOwnerChange}
                disabled={!newOwnerEmail}
              >
                <UserCheck className="h-4 w-4 mr-2" />
                确认变更
              </CustomButton>
            </DialogFooter>
          </DialogContent>
        </DialogOverlay>
      )}

      {/* 审批弹窗 */}
      {showApprovalModal && (
        <DialogOverlay onClick={() => setShowApprovalModal(false)}>
          <DialogContent onClick={(e) => e.stopPropagation()}>
            <DialogHeader>
              <DialogTitle>
                {approvalAction === "approve" ? (
                  <ThumbsUp className="h-5 w-5 text-blue-600" />
                ) : (
                  <ThumbsDown className="h-5 w-5 text-red-600" />
                )}
                审批操作
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-6 px-6">
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <FileText className="h-4 w-4 text-gray-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">工单信息</h4>
                    <p className="text-sm text-gray-600">
                      {orderInfo?.title} - {orderType}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <CustomLabel className="text-sm font-medium text-gray-700">审批决定</CustomLabel>
                <div className="grid grid-cols-2 gap-3">
                  <CustomButton
                    className={approvalAction === "approve" ? "variant-default" : "variant-outline"}
                    onClick={() => setApprovalAction("approve")}
                    style={{ height: '48px' }}
                  >
                    <ThumbsUp className="h-4 w-4 mr-2" />
                    同意
                  </CustomButton>
                  <CustomButton
                    className={approvalAction === "reject" ? "variant-destructive" : "variant-outline"}
                    onClick={() => setApprovalAction("reject")}
                    style={{ height: '48px' }}
                  >
                    <ThumbsDown className="h-4 w-4 mr-2" />
                    驳回
                  </CustomButton>
                </div>
              </div>

              <div className="space-y-3">
                <CustomLabel className="text-sm font-medium text-gray-700">备注</CustomLabel>
                <CustomTextarea
                  placeholder="请输入审批备注..."
                  value={remark}
                  onChange={(e) => setRemark(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <CustomButton
                className="variant-outline flex-1"
                onClick={() => setShowApprovalModal(false)}
              >
                取消
              </CustomButton>
              <CustomButton
                className={`flex-1 ${approvalAction === "reject" ? "variant-destructive" : ""}`}
                onClick={handleApproval}
              >
                {approvalAction === "approve" ? (
                  <ThumbsUp className="h-4 w-4 mr-2" />
                ) : (
                  <ThumbsDown className="h-4 w-4 mr-2" />
                )}
                确认{approvalAction === "approve" ? "同意" : "驳回"}
              </CustomButton>
            </DialogFooter>
          </DialogContent>
        </DialogOverlay>
      )}
    </>
  );
}
